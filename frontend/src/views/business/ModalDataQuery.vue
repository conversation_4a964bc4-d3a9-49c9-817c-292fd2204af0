<template>
  <div class="modal-data-query">
    <!-- 搜索卡片 -->
    <el-card class="search-card" shadow="never">
      <div class="search-form">
        <div class="form-row">
          <div class="form-group">
            <span class="form-label">车型：</span>
            <el-select
              v-model="searchForm.carModel"
              placeholder="请选择车型"
              class="form-select"
              clearable
            >
              <el-option
                v-for="item in carModelOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>

          <div class="form-group">
            <span class="form-label">零件：</span>
            <el-select
              v-model="searchForm.partName"
              placeholder="请选择零件"
              class="form-select"
              clearable
            >
              <el-option
                v-for="item in partOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>

          <div class="form-group">
            <el-button
              type="primary"
              :icon="Search"
              @click="handleSearch"
              :loading="loading"
              class="search-btn"
            >
              查询
            </el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 查询结果展示卡片 -->
    <el-card class="result-card" shadow="never">
      <template #header>
        <div class="card-header">
          <el-icon class="header-icon"><List /></el-icon>
          <span>查询结果展示</span>
          <div class="header-extra">
            <span class="result-count">共 {{ tableData.length }} 条数据</span>
          </div>
        </div>
      </template>

      <el-table
        :data="paginatedData"
        v-loading="loading"
        class="result-table"
        stripe
        :header-cell-style="{ backgroundColor: '#fafafa', color: '#606266' }"
      >
        <el-table-column prop="category" label="分类" width="100" align="center" />
        <el-table-column prop="partName" label="零件名称" width="150" />
        <el-table-column prop="frequency" label="频率(Hz)" width="120" align="center">
          <template #default="scope">
            <span class="frequency-value">{{ scope.row.frequency }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="modalType" label="模态类型" width="150" />
        <el-table-column label="操作" width="120" align="center">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              text
              @click="viewModalShape(scope.row)"
            >
              查看振型
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="tableData.length"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, List } from '@element-plus/icons-vue'

// 搜索表单
const searchForm = ref({
  carModel: '',
  partName: ''
})

// 加载状态
const loading = ref(false)

// 分页
const currentPage = ref(1)
const pageSize = ref(10)

// 车型选项
const carModelOptions = ref([
  { label: 'Model A', value: 'model-a' },
  { label: 'Model B', value: 'model-b' },
  { label: 'Model C', value: 'model-c' },
  { label: 'Model D', value: 'model-d' }
])

// 零件选项
const partOptions = ref([
  { label: '前保险杠', value: 'front-bumper' },
  { label: '车门板', value: 'door-panel' },
  { label: '仪表盘骨架', value: 'dashboard-frame' },
  { label: '后备箱盖', value: 'trunk-lid' },
  { label: '引擎盖', value: 'hood' },
  { label: '车顶', value: 'roof' }
])

// 表格数据
const tableData = ref([
  {
    id: 1,
    category: '底盘',
    partName: '前保险杠',
    frequency: '35.2',
    modalType: '1阶弯曲模态'
  },
  {
    id: 2,
    category: '车身',
    partName: '车门板',
    frequency: '48.6',
    modalType: '1阶扭转模态'
  },
  {
    id: 3,
    category: '结构件',
    partName: '仪表盘骨架',
    frequency: '62.0',
    modalType: '2阶弯曲模态'
  },
  {
    id: 4,
    category: '车身',
    partName: '后备箱盖',
    frequency: '28.4',
    modalType: '1阶弯曲模态'
  },
  {
    id: 5,
    category: '底盘',
    partName: '引擎盖',
    frequency: '41.8',
    modalType: '1阶扭转模态'
  },
  {
    id: 6,
    category: '结构件',
    partName: '车顶',
    frequency: '55.3',
    modalType: '2阶扭转模态'
  }
])

// 分页数据
const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return tableData.value.slice(start, end)
})

// 搜索处理
const handleSearch = async () => {
  loading.value = true
  try {
    // 模拟API请求
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('查询完成')
  } catch (error) {
    ElMessage.error('查询失败')
  } finally {
    loading.value = false
  }
}

// 查看振型
const viewModalShape = (row) => {
  ElMessage.info(`查看 ${row.partName} 的振型数据`)
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
}

const handleCurrentChange = (val) => {
  currentPage.value = val
}

onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.modal-data-query {
  padding: 0;
  background-color: #f5f7fa;
}

/* 卡片样式 */
.search-card,
.result-card {
  margin-bottom: 16px;
  border-radius: 6px;
  border: 1px solid #e7e7e7;
}

.search-card {
  background-color: #fff;
}

.result-card {
  background-color: #fff;
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #1f2937;
}

.header-icon {
  color: #0052d9;
  font-size: 16px;
}

.header-extra {
  margin-left: auto;
}

.result-count {
  font-size: 14px;
  color: #6b7280;
}

/* 搜索表单 */
.search-form {
  padding: 0;
}

.form-row {
  display: flex;
  align-items: center;
  gap: 24px;
  flex-wrap: wrap;
}

.form-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-label {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
  white-space: nowrap;
  min-width: fit-content;
}

.form-select {
  width: 180px;
}

.search-btn {
  height: 32px;
  padding: 0 20px;
  border-radius: 3px;
  font-size: 14px;
  margin-left: 8px;
}

/* 表格样式 */
.result-table {
  border-radius: 6px;
  overflow: hidden;
}

:deep(.el-table) {
  border: 1px solid #e7e7e7;
}

:deep(.el-table th) {
  background-color: #f8f9fa !important;
  border-bottom: 1px solid #e7e7e7;
  font-weight: 500;
  color: #374151;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f1f3f4;
}

:deep(.el-table .el-table__row:hover > td) {
  background-color: #f8f9fa;
}

.frequency-value {
  font-family: 'Monaco', 'Menlo', monospace;
  font-weight: 500;
  color: #0052d9;
}

/* 分页器 */
.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f1f3f4;
}

:deep(.el-pagination) {
  --el-pagination-font-size: 14px;
}

:deep(.el-pagination .btn-prev),
:deep(.el-pagination .btn-next) {
  border-radius: 3px;
}

:deep(.el-pagination .el-pager li) {
  border-radius: 3px;
  margin: 0 2px;
}

:deep(.el-pagination .el-pager li.is-active) {
  background-color: #0052d9;
  color: white;
}

/* Element Plus 组件样式覆盖 */
:deep(.search-card .el-card__body) {
  padding: 20px;
}

:deep(.result-card .el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid #f1f3f4;
  background-color: #fafbfc;
}

:deep(.result-card .el-card__body) {
  padding: 20px;
}

:deep(.el-select) {
  --el-select-border-color-hover: #0052d9;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 3px;
}

:deep(.el-button--primary) {
  background-color: #0052d9;
  border-color: #0052d9;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

:deep(.el-button--text) {
  color: #0052d9;
}

:deep(.el-button--text:hover) {
  color: #1890ff;
  background-color: #f0f8ff;
}
</style>
